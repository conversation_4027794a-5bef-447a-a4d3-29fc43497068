"""
WiFi指纹数据扩充主程序
使用WGAN-GP进行数据生成和扩充
"""

import torch
import torch.utils.data as data
import numpy as np
import pandas as pd
import os
from scipy.io import savemat
import matplotlib.pyplot as plt

from data_preprocessing import DataPreprocessor
from wgan_gp_model import WGAN_GP
from evaluation_metrics import evaluate_generation_quality

class WiFiDataset(data.Dataset):
    """WiFi数据集类"""
    def __init__(self, data):
        self.data = torch.FloatTensor(data)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]

def denormalize_data(normalized_data, min_rssi=-100, max_rssi=-30):
    """反归一化数据"""
    # 从[-1, 1]恢复到原始RSSI范围
    denormalized = (normalized_data + 1) / 2 * (max_rssi - min_rssi) + min_rssi
    return denormalized

def process_generated_data(generated_data, min_rssi=-100, max_rssi=-30):
    """处理生成的数据"""
    # 反归一化
    processed_data = denormalize_data(generated_data, min_rssi, max_rssi)
    
    # 将100替换为-100，正数替换为-100
    processed_data[processed_data == 100] = -100
    processed_data[processed_data > 0] = -100
    
    # 确保数据在合理范围内
    processed_data = np.clip(processed_data, min_rssi, max_rssi)
    
    return processed_data

def merge_datasets(original_data, generated_data, wap_columns, positions):
    """合并原始数据和生成数据"""
    print("\n合并数据集...")
    
    # 确保生成数据的形状正确
    if generated_data.shape[1] != len(wap_columns):
        print(f"警告: 生成数据特征数({generated_data.shape[1]})与WAP列数({len(wap_columns)})不匹配")
        # 截断或填充
        if generated_data.shape[1] > len(wap_columns):
            generated_data = generated_data[:, :len(wap_columns)]
        else:
            # 填充缺失的列
            padding = np.full((generated_data.shape[0], len(wap_columns) - generated_data.shape[1]), -100)
            generated_data = np.concatenate([generated_data, padding], axis=1)
    
    # 为生成数据创建位置信息（可以是随机的或基于原始数据的插值）
    num_generated = generated_data.shape[0]
    
    # 使用原始位置数据的范围生成随机位置
    lon_min, lon_max = positions[:, 0].min(), positions[:, 0].max()
    lat_min, lat_max = positions[:, 1].min(), positions[:, 1].max()
    
    generated_positions = np.column_stack([
        np.random.uniform(lon_min, lon_max, num_generated),
        np.random.uniform(lat_min, lat_max, num_generated)
    ])
    
    # 创建完整的数据集
    # 原始数据
    original_wap = original_data[wap_columns].values
    original_pos = original_data[['LONGITUDE', 'LATITUDE']].values
    
    # 合并WAP数据
    merged_wap = np.vstack([original_wap, generated_data])
    merged_positions = np.vstack([original_pos, generated_positions])
    
    # 创建DataFrame
    merged_df = pd.DataFrame(merged_wap, columns=wap_columns)
    merged_df['LONGITUDE'] = merged_positions[:, 0]
    merged_df['LATITUDE'] = merged_positions[:, 1]
    
    # 添加数据来源标识
    data_source = ['original'] * len(original_data) + ['generated'] * num_generated
    merged_df['DATA_SOURCE'] = data_source
    
    print(f"原始数据: {len(original_data)} 条")
    print(f"生成数据: {num_generated} 条")
    print(f"合并后数据: {len(merged_df)} 条")
    
    return merged_df

def save_final_dataset(merged_df, building_id=0, floor_id=3):
    """保存最终的数据集"""
    output_dir = 'final_dataset'
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存为CSV
    csv_file = os.path.join(output_dir, f'building{building_id}_floor{floor_id}_augmented.csv')
    merged_df.to_csv(csv_file, index=False)
    print(f"增强数据集已保存到: {csv_file}")
    
    # 保存为MAT格式
    mat_file = os.path.join(output_dir, f'building{building_id}_floor{floor_id}_augmented.mat')
    
    # 分离不同类型的数据
    wap_columns = [col for col in merged_df.columns if col.startswith('WAP')]
    wap_data = merged_df[wap_columns].values
    positions = merged_df[['LONGITUDE', 'LATITUDE']].values
    data_source = merged_df['DATA_SOURCE'].values
    
    # 分离原始和生成的数据
    original_mask = data_source == 'original'
    generated_mask = data_source == 'generated'
    
    original_wap = wap_data[original_mask]
    generated_wap = wap_data[generated_mask]
    original_pos = positions[original_mask]
    generated_pos = positions[generated_mask]
    
    savemat(mat_file, {
        'all_wap_data': wap_data,
        'all_positions': positions,
        'original_wap_data': original_wap,
        'generated_wap_data': generated_wap,
        'original_positions': original_pos,
        'generated_positions': generated_pos,
        'wap_columns': wap_columns,
        'data_source': data_source,
        'building_id': building_id,
        'floor_id': floor_id,
        'dataset_info': f'Augmented dataset for Building {building_id} Floor {floor_id}'
    })
    print(f"增强数据集已保存到: {mat_file}")

def main():
    """主函数"""
    print("=== WiFi指纹数据扩充系统 ===")
    
    # 设置参数
    BUILDING_ID = 0
    FLOOR_ID = 3
    BATCH_SIZE = 64
    EPOCHS = 1000
    NUM_GENERATED_SAMPLES = 1000  # 生成样本数量
    
    # 1. 数据预处理
    print("\n1. 数据预处理...")
    preprocessor = DataPreprocessor()
    preprocessor.load_data()
    
    wap_features, positions, wap_columns, original_data = preprocessor.extract_target_data(
        building_id=BUILDING_ID, floor_id=FLOOR_ID
    )
    
    normalized_data, processed_data = preprocessor.preprocess_wap_data(wap_features)
    
    # 2. 创建数据加载器
    print("\n2. 创建数据加载器...")
    dataset = WiFiDataset(normalized_data)
    dataloader = data.DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True, drop_last=True)
    
    print(f"数据集大小: {len(dataset)}")
    print(f"批次数量: {len(dataloader)}")
    
    # 3. 初始化WGAN-GP模型
    print("\n3. 初始化WGAN-GP模型...")
    wgan_gp = WGAN_GP(
        data_dim=normalized_data.shape[1],
        noise_dim=100,
        lr=1e-4,
        lambda_gp=10,
        n_critic=5
    )
    
    # 4. 训练模型
    print("\n4. 训练WGAN-GP模型...")
    wgan_gp.train(dataloader, epochs=EPOCHS, save_interval=200)
    
    # 5. 生成数据
    print(f"\n5. 生成{NUM_GENERATED_SAMPLES}个样本...")
    generated_normalized = wgan_gp.generate_samples(NUM_GENERATED_SAMPLES)
    
    # 处理生成的数据
    generated_processed = process_generated_data(generated_normalized)
    
    print(f"生成数据形状: {generated_processed.shape}")
    print(f"生成数据范围: [{generated_processed.min():.2f}, {generated_processed.max():.2f}]")
    
    # 6. 评估生成质量
    print("\n6. 评估生成质量...")
    metrics, evaluator = evaluate_generation_quality(processed_data, generated_processed)
    
    # 7. 合并数据集
    print("\n7. 合并数据集...")
    merged_df = merge_datasets(original_data, generated_processed, wap_columns, positions)
    
    # 8. 保存最终数据集
    print("\n8. 保存最终数据集...")
    save_final_dataset(merged_df, BUILDING_ID, FLOOR_ID)
    
    # 9. 绘制训练历史
    print("\n9. 生成可视化结果...")
    wgan_gp.plot_training_history()
    evaluator.plot_metrics_history()
    
    print("\n=== 数据扩充完成! ===")
    print(f"最终数据集包含 {len(merged_df)} 条记录")
    print(f"其中原始数据: {len(original_data)} 条")
    print(f"生成数据: {NUM_GENERATED_SAMPLES} 条")
    
    return {
        'merged_dataset': merged_df,
        'original_data': original_data,
        'generated_data': generated_processed,
        'metrics': metrics,
        'wgan_gp': wgan_gp
    }

if __name__ == "__main__":
    result = main()
