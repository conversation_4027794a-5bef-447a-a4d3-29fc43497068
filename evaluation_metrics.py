"""
评价指标计算模块
包括KL散度、JS散度和Was<PERSON>stein距离
"""

import numpy as np
from scipy import stats
from scipy.spatial.distance import j<PERSON><PERSON><PERSON><PERSON>
from scipy.stats import wasserstein_distance
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler

class EvaluationMetrics:
    """评价指标计算器"""
    
    def __init__(self):
        self.metrics_history = {
            'kl_divergence': [],
            'js_divergence': [],
            'wasserstein_distance': []
        }
    
    def compute_histogram(self, data, bins=50, range_vals=None):
        """计算数据的直方图分布"""
        if range_vals is None:
            range_vals = (data.min(), data.max())
        
        hist, bin_edges = np.histogram(data.flatten(), bins=bins, range=range_vals, density=True)
        # 避免零概率
        hist = hist + 1e-10
        hist = hist / hist.sum()
        return hist, bin_edges
    
    def kl_divergence(self, real_data, generated_data, bins=50):
        """计算KL散度"""
        # 确定共同的范围
        min_val = min(real_data.min(), generated_data.min())
        max_val = max(real_data.max(), generated_data.max())
        range_vals = (min_val, max_val)
        
        # 计算直方图
        p, _ = self.compute_histogram(real_data, bins, range_vals)
        q, _ = self.compute_histogram(generated_data, bins, range_vals)
        
        # 计算KL散度
        kl_div = stats.entropy(p, q)
        return kl_div
    
    def js_divergence(self, real_data, generated_data, bins=50):
        """计算JS散度"""
        # 确定共同的范围
        min_val = min(real_data.min(), generated_data.min())
        max_val = max(real_data.max(), generated_data.max())
        range_vals = (min_val, max_val)
        
        # 计算直方图
        p, _ = self.compute_histogram(real_data, bins, range_vals)
        q, _ = self.compute_histogram(generated_data, bins, range_vals)
        
        # 计算JS散度
        js_div = jensenshannon(p, q) ** 2
        return js_div
    
    def wasserstein_distance_1d(self, real_data, generated_data):
        """计算一维Wasserstein距离"""
        return wasserstein_distance(real_data.flatten(), generated_data.flatten())
    
    def wasserstein_distance_multivariate(self, real_data, generated_data, num_samples=1000):
        """计算多变量Wasserstein距离的近似"""
        # 随机采样以减少计算复杂度
        if len(real_data) > num_samples:
            real_indices = np.random.choice(len(real_data), num_samples, replace=False)
            real_sample = real_data[real_indices]
        else:
            real_sample = real_data
            
        if len(generated_data) > num_samples:
            gen_indices = np.random.choice(len(generated_data), num_samples, replace=False)
            gen_sample = generated_data[gen_indices]
        else:
            gen_sample = generated_data
        
        # 计算每个特征的Wasserstein距离并取平均
        distances = []
        for i in range(real_sample.shape[1]):
            dist = wasserstein_distance(real_sample[:, i], gen_sample[:, i])
            distances.append(dist)
        
        return np.mean(distances)
    
    def compute_all_metrics(self, real_data, generated_data, bins=50):
        """计算所有评价指标"""
        print("计算评价指标...")
        
        # KL散度
        kl_div = self.kl_divergence(real_data, generated_data, bins)
        
        # JS散度
        js_div = self.js_divergence(real_data, generated_data, bins)
        
        # Wasserstein距离
        w_dist = self.wasserstein_distance_multivariate(real_data, generated_data)
        
        # 保存结果
        metrics = {
            'kl_divergence': kl_div,
            'js_divergence': js_div,
            'wasserstein_distance': w_dist
        }
        
        # 更新历史记录
        for key, value in metrics.items():
            self.metrics_history[key].append(value)
        
        return metrics
    
    def print_metrics(self, metrics):
        """打印评价指标"""
        print("\n=== 评价指标结果 ===")
        print(f"KL散度 (KL Divergence): {metrics['kl_divergence']:.6f}")
        print(f"JS散度 (JS Divergence): {metrics['js_divergence']:.6f}")
        print(f"Wasserstein距离: {metrics['wasserstein_distance']:.6f}")
        print("=" * 30)
    
    def plot_data_comparison(self, real_data, generated_data, save_path='data_comparison.png'):
        """绘制真实数据和生成数据的比较图"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 选择几个特征进行可视化
        num_features = min(6, real_data.shape[1])
        feature_indices = np.linspace(0, real_data.shape[1]-1, num_features, dtype=int)
        
        for i, feature_idx in enumerate(feature_indices):
            row = i // 3
            col = i % 3
            
            # 绘制直方图
            axes[row, col].hist(real_data[:, feature_idx], bins=30, alpha=0.7, 
                               label='Real', density=True, color='blue')
            axes[row, col].hist(generated_data[:, feature_idx], bins=30, alpha=0.7, 
                               label='Generated', density=True, color='red')
            axes[row, col].set_title(f'Feature {feature_idx}')
            axes[row, col].legend()
            axes[row, col].set_xlabel('Value')
            axes[row, col].set_ylabel('Density')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_metrics_history(self, save_path='metrics_history.png'):
        """绘制评价指标历史"""
        if not any(self.metrics_history.values()):
            print("没有历史数据可绘制")
            return
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # KL散度
        if self.metrics_history['kl_divergence']:
            axes[0].plot(self.metrics_history['kl_divergence'], 'b-o')
            axes[0].set_title('KL Divergence')
            axes[0].set_xlabel('Evaluation')
            axes[0].set_ylabel('KL Divergence')
        
        # JS散度
        if self.metrics_history['js_divergence']:
            axes[1].plot(self.metrics_history['js_divergence'], 'g-o')
            axes[1].set_title('JS Divergence')
            axes[1].set_xlabel('Evaluation')
            axes[1].set_ylabel('JS Divergence')
        
        # Wasserstein距离
        if self.metrics_history['wasserstein_distance']:
            axes[2].plot(self.metrics_history['wasserstein_distance'], 'r-o')
            axes[2].set_title('Wasserstein Distance')
            axes[2].set_xlabel('Evaluation')
            axes[2].set_ylabel('Wasserstein Distance')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_correlation_matrix(self, real_data, generated_data, save_path='correlation_comparison.png'):
        """绘制相关性矩阵比较"""
        # 随机选择一些特征以避免图像过大
        num_features = min(20, real_data.shape[1])
        feature_indices = np.random.choice(real_data.shape[1], num_features, replace=False)
        
        real_subset = real_data[:, feature_indices]
        gen_subset = generated_data[:, feature_indices]
        
        # 计算相关性矩阵
        real_corr = np.corrcoef(real_subset.T)
        gen_corr = np.corrcoef(gen_subset.T)
        
        # 绘制比较图
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))
        
        # 真实数据相关性
        sns.heatmap(real_corr, ax=axes[0], cmap='coolwarm', center=0, 
                   square=True, cbar_kws={'shrink': 0.8})
        axes[0].set_title('Real Data Correlation')
        
        # 生成数据相关性
        sns.heatmap(gen_corr, ax=axes[1], cmap='coolwarm', center=0, 
                   square=True, cbar_kws={'shrink': 0.8})
        axes[1].set_title('Generated Data Correlation')
        
        # 差异
        diff_corr = np.abs(real_corr - gen_corr)
        sns.heatmap(diff_corr, ax=axes[2], cmap='Reds', 
                   square=True, cbar_kws={'shrink': 0.8})
        axes[2].set_title('Correlation Difference')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

def evaluate_generation_quality(real_data, generated_data):
    """评估生成质量的主函数"""
    evaluator = EvaluationMetrics()
    
    # 计算所有指标
    metrics = evaluator.compute_all_metrics(real_data, generated_data)
    
    # 打印结果
    evaluator.print_metrics(metrics)
    
    # 绘制比较图
    evaluator.plot_data_comparison(real_data, generated_data)
    evaluator.plot_correlation_matrix(real_data, generated_data)
    
    return metrics, evaluator
