"""
WGAN-GP模型实现
用于WiFi指纹数据生成
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.autograd as autograd
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import os

class Generator(nn.Module):
    """生成器网络"""
    def __init__(self, noise_dim=100, output_dim=520, hidden_dims=[256, 512, 1024]):
        super(Generator, self).__init__()

        layers = []
        input_dim = noise_dim

        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(True)
            ])
            input_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(input_dim, output_dim))
        layers.append(nn.Tanh())  # 输出范围[-1, 1]

        self.model = nn.Sequential(*layers)

    def forward(self, noise):
        return self.model(noise)

class Discriminator(nn.Module):
    """判别器网络"""
    def __init__(self, input_dim=520, hidden_dims=[1024, 512, 256]):
        super(Discriminator, self).__init__()

        layers = []
        current_dim = input_dim

        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.LeakyReLU(0.2, True),
                nn.Dropout(0.3)
            ])
            current_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(current_dim, 1))

        self.model = nn.Sequential(*layers)

    def forward(self, x):
        return self.model(x)

class WGAN_GP:
    """WGAN-GP训练器"""
    def __init__(self, data_dim=520, noise_dim=100, lr=1e-4, beta1=0.5, beta2=0.9,
                 lambda_gp=10, n_critic=5, device='cuda'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {self.device}")

        self.data_dim = data_dim
        self.noise_dim = noise_dim
        self.lambda_gp = lambda_gp
        self.n_critic = n_critic

        # 初始化网络
        self.generator = Generator(noise_dim, data_dim).to(self.device)
        self.discriminator = Discriminator(data_dim).to(self.device)

        # 初始化优化器
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=lr, betas=(beta1, beta2))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=lr, betas=(beta1, beta2))

        # 训练历史
        self.g_losses = []
        self.d_losses = []
        self.w_distances = []

    def gradient_penalty(self, real_data, fake_data):
        """计算梯度惩罚"""
        batch_size = real_data.size(0)

        # 随机插值
        alpha = torch.rand(batch_size, 1).to(self.device)
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)

        # 计算判别器输出
        d_interpolated = self.discriminator(interpolated)

        # 计算梯度
        gradients = autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        # 计算梯度惩罚
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()

        return gradient_penalty

    def train_discriminator(self, real_data):
        """训练判别器"""
        batch_size = real_data.size(0)

        # 生成假数据
        noise = torch.randn(batch_size, self.noise_dim).to(self.device)
        fake_data = self.generator(noise).detach()

        # 计算判别器损失
        d_real = self.discriminator(real_data)
        d_fake = self.discriminator(fake_data)

        # Wasserstein损失
        w_distance = d_real.mean() - d_fake.mean()

        # 梯度惩罚
        gp = self.gradient_penalty(real_data, fake_data)

        # 总损失
        d_loss = -w_distance + self.lambda_gp * gp

        # 更新判别器
        self.d_optimizer.zero_grad()
        d_loss.backward()
        self.d_optimizer.step()

        return d_loss.item(), w_distance.item()

    def train_generator(self, batch_size):
        """训练生成器"""
        noise = torch.randn(batch_size, self.noise_dim).to(self.device)
        fake_data = self.generator(noise)

        # 生成器损失
        g_loss = -self.discriminator(fake_data).mean()

        # 更新生成器
        self.g_optimizer.zero_grad()
        g_loss.backward()
        self.g_optimizer.step()

        return g_loss.item()

    def train(self, dataloader, epochs=1000, save_interval=100):
        """训练WGAN-GP"""
        print(f"开始训练WGAN-GP，共{epochs}个epoch...")

        for epoch in tqdm(range(epochs), desc="训练进度"):
            d_losses_epoch = []
            w_distances_epoch = []

            for i, real_data in enumerate(dataloader):
                real_data = real_data.to(self.device)
                batch_size = real_data.size(0)

                # 训练判别器
                d_loss, w_distance = self.train_discriminator(real_data)
                d_losses_epoch.append(d_loss)
                w_distances_epoch.append(w_distance)

                # 每n_critic次训练一次生成器
                if i % self.n_critic == 0:
                    g_loss = self.train_generator(batch_size)
                    self.g_losses.append(g_loss)

            # 记录平均损失
            avg_d_loss = np.mean(d_losses_epoch)
            avg_w_distance = np.mean(w_distances_epoch)
            self.d_losses.append(avg_d_loss)
            self.w_distances.append(avg_w_distance)

            # 打印进度
            if (epoch + 1) % 100 == 0:
                print(f"Epoch [{epoch+1}/{epochs}] - "
                      f"D_loss: {avg_d_loss:.4f}, "
                      f"G_loss: {self.g_losses[-1]:.4f}, "
                      f"W_distance: {avg_w_distance:.4f}")

            # 保存模型
            if (epoch + 1) % save_interval == 0:
                self.save_models(epoch + 1)

        print("训练完成!")

    def generate_samples(self, num_samples):
        """生成样本"""
        self.generator.eval()
        with torch.no_grad():
            noise = torch.randn(num_samples, self.noise_dim).to(self.device)
            generated_data = self.generator(noise)
        self.generator.train()
        return generated_data.cpu().numpy()

    def save_models(self, epoch):
        """保存模型"""
        os.makedirs('models', exist_ok=True)
        torch.save({
            'generator': self.generator.state_dict(),
            'discriminator': self.discriminator.state_dict(),
            'g_optimizer': self.g_optimizer.state_dict(),
            'd_optimizer': self.d_optimizer.state_dict(),
            'epoch': epoch,
            'g_losses': self.g_losses,
            'd_losses': self.d_losses,
            'w_distances': self.w_distances
        }, f'models/wgan_gp_epoch_{epoch}.pth')

    def load_models(self, checkpoint_path):
        """加载模型"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=False)
        self.generator.load_state_dict(checkpoint['generator'])
        self.discriminator.load_state_dict(checkpoint['discriminator'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer'])
        self.g_losses = checkpoint['g_losses']
        self.d_losses = checkpoint['d_losses']
        self.w_distances = checkpoint['w_distances']
        print(f"模型已从 {checkpoint_path} 加载")

    def plot_training_history(self):
        """绘制训练历史"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # 生成器损失
        axes[0].plot(self.g_losses)
        axes[0].set_title('Generator Loss')
        axes[0].set_xlabel('Iteration')
        axes[0].set_ylabel('Loss')

        # 判别器损失
        axes[1].plot(self.d_losses)
        axes[1].set_title('Discriminator Loss')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Loss')

        # Wasserstein距离
        axes[2].plot(self.w_distances)
        axes[2].set_title('Wasserstein Distance')
        axes[2].set_xlabel('Epoch')
        axes[2].set_ylabel('Distance')

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
