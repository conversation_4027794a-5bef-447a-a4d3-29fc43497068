"""
WiFi指纹数据预处理模块
提取楼栋0楼层3的数据并进行预处理
"""

import pandas as pd
import numpy as np
import os
from scipy.io import savemat, loadmat
import matplotlib.pyplot as plt
import seaborn as sns

class DataPreprocessor:
    def __init__(self, data_path='UJIndoorLoc'):
        self.data_path = data_path
        self.training_file = os.path.join(data_path, 'trainingData.csv')
        self.validation_file = os.path.join(data_path, 'validationData.csv')
        
    def load_data(self):
        """加载原始数据"""
        print("加载数据...")
        self.train_data = pd.read_csv(self.training_file)
        self.validation_data = pd.read_csv(self.validation_file)
        
        print(f"训练数据形状: {self.train_data.shape}")
        print(f"验证数据形状: {self.validation_data.shape}")
        
        # 显示数据分布
        self.show_data_distribution()
        
    def show_data_distribution(self):
        """显示数据分布"""
        print("\n=== 数据分布分析 ===")
        print("楼层分布:")
        print(self.train_data['FLOOR'].value_counts().sort_index())
        print("\n建筑分布:")
        print(self.train_data['BUILDINGID'].value_counts().sort_index())
        
        # 楼栋0的楼层分布
        building0_data = self.train_data[self.train_data['BUILDINGID'] == 0]
        print(f"\n楼栋0的楼层分布:")
        print(building0_data['FLOOR'].value_counts().sort_index())
        
        # 楼栋0楼层3的数据量
        target_data = self.train_data[(self.train_data['BUILDINGID'] == 0) & 
                                     (self.train_data['FLOOR'] == 3)]
        print(f"\n楼栋0楼层3的数据量: {len(target_data)}")
        
    def extract_target_data(self, building_id=0, floor_id=3):
        """提取目标楼栋和楼层的数据"""
        print(f"\n提取楼栋{building_id}楼层{floor_id}的数据...")
        
        # 提取目标数据
        target_data = self.train_data[(self.train_data['BUILDINGID'] == building_id) & 
                                     (self.train_data['FLOOR'] == floor_id)]
        
        print(f"提取到的数据量: {len(target_data)}")
        
        if len(target_data) == 0:
            raise ValueError(f"没有找到楼栋{building_id}楼层{floor_id}的数据")
        
        # 分离WAP特征和位置信息
        wap_columns = [col for col in target_data.columns if col.startswith('WAP')]
        position_columns = ['LONGITUDE', 'LATITUDE']
        
        print(f"WAP特征数量: {len(wap_columns)}")
        print(f"位置信息列: {position_columns}")
        
        # 提取特征和位置
        wap_features = target_data[wap_columns].values
        positions = target_data[position_columns].values
        
        # 保存原始数据
        self.save_original_data(target_data, building_id, floor_id)
        
        return wap_features, positions, wap_columns, target_data
    
    def preprocess_wap_data(self, wap_features):
        """预处理WAP数据"""
        print("\n预处理WAP数据...")
        
        # 显示原始数据统计
        print("原始数据统计:")
        print(f"数据形状: {wap_features.shape}")
        print(f"数值范围: [{np.min(wap_features)}, {np.max(wap_features)}]")
        print(f"唯一值数量: {len(np.unique(wap_features))}")
        
        # 统计不同数值的分布
        unique_values, counts = np.unique(wap_features, return_counts=True)
        print("数值分布:")
        for val, count in zip(unique_values[:10], counts[:10]):  # 显示前10个
            print(f"  {val}: {count} ({count/wap_features.size*100:.2f}%)")
        
        # 数据预处理：将100替换为-100，正数替换为-100
        processed_data = wap_features.copy()
        
        # 将100替换为-100
        processed_data[processed_data == 100] = -100
        
        # 将正数替换为-100
        processed_data[processed_data > 0] = -100
        
        print("\n预处理后数据统计:")
        print(f"数值范围: [{np.min(processed_data)}, {np.max(processed_data)}]")
        print(f"唯一值数量: {len(np.unique(processed_data))}")
        
        # 归一化到[-1, 1]范围用于训练
        # 假设RSSI值范围为[-100, -30]
        min_rssi = -100
        max_rssi = -30
        
        # 将数据限制在合理范围内
        processed_data = np.clip(processed_data, min_rssi, max_rssi)
        
        # 归一化到[-1, 1]
        normalized_data = 2 * (processed_data - min_rssi) / (max_rssi - min_rssi) - 1
        
        print(f"归一化后数值范围: [{np.min(normalized_data):.3f}, {np.max(normalized_data):.3f}]")
        
        return normalized_data, processed_data
    
    def save_original_data(self, data, building_id, floor_id):
        """保存原始提取的数据"""
        output_dir = 'processed_data'
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存为CSV
        csv_file = os.path.join(output_dir, f'building{building_id}_floor{floor_id}_original.csv')
        data.to_csv(csv_file, index=False)
        print(f"原始数据已保存到: {csv_file}")
        
        # 保存为MAT格式
        mat_file = os.path.join(output_dir, f'building{building_id}_floor{floor_id}_original.mat')
        
        # 分离WAP特征和其他信息
        wap_columns = [col for col in data.columns if col.startswith('WAP')]
        wap_data = data[wap_columns].values
        positions = data[['LONGITUDE', 'LATITUDE']].values
        
        savemat(mat_file, {
            'wap_data': wap_data,
            'positions': positions,
            'wap_columns': wap_columns,
            'building_id': building_id,
            'floor_id': floor_id,
            'data_info': f'Building {building_id} Floor {floor_id} Original Data'
        })
        print(f"原始数据已保存到: {mat_file}")

def main():
    """主函数"""
    # 创建数据预处理器
    preprocessor = DataPreprocessor()
    
    # 加载数据
    preprocessor.load_data()
    
    # 提取楼栋0楼层3的数据
    wap_features, positions, wap_columns, original_data = preprocessor.extract_target_data(
        building_id=0, floor_id=3
    )
    
    # 预处理WAP数据
    normalized_data, processed_data = preprocessor.preprocess_wap_data(wap_features)
    
    print(f"\n数据预处理完成!")
    print(f"原始数据形状: {wap_features.shape}")
    print(f"处理后数据形状: {normalized_data.shape}")
    
    return {
        'normalized_data': normalized_data,
        'processed_data': processed_data,
        'positions': positions,
        'wap_columns': wap_columns,
        'original_data': original_data
    }

if __name__ == "__main__":
    result = main()
