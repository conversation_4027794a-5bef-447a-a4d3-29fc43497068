"""
最终数据生成和保存脚本（不包含绘图）
"""

import torch
import numpy as np
import pandas as pd
import os
from scipy.io import savemat

from data_preprocessing import DataPreprocessor
from wgan_gp_model import WGAN_GP

def denormalize_data(normalized_data, min_rssi=-100, max_rssi=-30):
    """反归一化数据"""
    denormalized = (normalized_data + 1) / 2 * (max_rssi - min_rssi) + min_rssi
    return denormalized

def process_generated_data(generated_data, min_rssi=-100, max_rssi=-30):
    """处理生成的数据"""
    processed_data = denormalize_data(generated_data, min_rssi, max_rssi)
    processed_data[processed_data == 100] = -100
    processed_data[processed_data > 0] = -100
    processed_data = np.clip(processed_data, min_rssi, max_rssi)
    return processed_data

def compute_simple_metrics(real_data, generated_data):
    """计算简单的评价指标"""
    from scipy import stats
    from scipy.spatial.distance import jensenshannon
    from scipy.stats import wasserstein_distance
    
    # 计算直方图
    def compute_histogram(data, bins=50):
        hist, _ = np.histogram(data.flatten(), bins=bins, density=True)
        hist = hist + 1e-10
        hist = hist / hist.sum()
        return hist
    
    # KL散度
    p = compute_histogram(real_data)
    q = compute_histogram(generated_data)
    kl_div = stats.entropy(p, q)
    
    # JS散度
    js_div = jensenshannon(p, q) ** 2
    
    # Wasserstein距离
    w_dist = wasserstein_distance(real_data.flatten(), generated_data.flatten())
    
    return {
        'kl_divergence': kl_div,
        'js_divergence': js_div,
        'wasserstein_distance': w_dist
    }

def merge_datasets(original_data, generated_data, wap_columns, positions):
    """合并原始数据和生成数据"""
    print("\n合并数据集...")
    
    if generated_data.shape[1] != len(wap_columns):
        print(f"调整生成数据维度: {generated_data.shape[1]} -> {len(wap_columns)}")
        if generated_data.shape[1] > len(wap_columns):
            generated_data = generated_data[:, :len(wap_columns)]
        else:
            padding = np.full((generated_data.shape[0], len(wap_columns) - generated_data.shape[1]), -100)
            generated_data = np.concatenate([generated_data, padding], axis=1)
    
    num_generated = generated_data.shape[0]
    
    # 生成位置信息
    lon_min, lon_max = positions[:, 0].min(), positions[:, 0].max()
    lat_min, lat_max = positions[:, 1].min(), positions[:, 1].max()
    
    generated_positions = np.column_stack([
        np.random.uniform(lon_min, lon_max, num_generated),
        np.random.uniform(lat_min, lat_max, num_generated)
    ])
    
    # 合并数据
    original_wap = original_data[wap_columns].values
    original_pos = original_data[['LONGITUDE', 'LATITUDE']].values
    
    merged_wap = np.vstack([original_wap, generated_data])
    merged_positions = np.vstack([original_pos, generated_positions])
    
    # 创建DataFrame
    merged_df = pd.DataFrame(merged_wap, columns=wap_columns)
    merged_df['LONGITUDE'] = merged_positions[:, 0]
    merged_df['LATITUDE'] = merged_positions[:, 1]
    
    data_source = ['original'] * len(original_data) + ['generated'] * num_generated
    merged_df['DATA_SOURCE'] = data_source
    
    print(f"原始数据: {len(original_data)} 条")
    print(f"生成数据: {num_generated} 条")
    print(f"合并后数据: {len(merged_df)} 条")
    
    return merged_df

def save_final_dataset(merged_df, building_id=0, floor_id=3):
    """保存最终的数据集"""
    output_dir = 'final_dataset'
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存为CSV
    csv_file = os.path.join(output_dir, f'building{building_id}_floor{floor_id}_augmented.csv')
    merged_df.to_csv(csv_file, index=False)
    print(f"增强数据集已保存到: {csv_file}")
    
    # 保存为MAT格式
    mat_file = os.path.join(output_dir, f'building{building_id}_floor{floor_id}_augmented.mat')
    
    wap_columns = [col for col in merged_df.columns if col.startswith('WAP')]
    wap_data = merged_df[wap_columns].values
    positions = merged_df[['LONGITUDE', 'LATITUDE']].values
    data_source = merged_df['DATA_SOURCE'].values
    
    original_mask = data_source == 'original'
    generated_mask = data_source == 'generated'
    
    original_wap = wap_data[original_mask]
    generated_wap = wap_data[generated_mask]
    original_pos = positions[original_mask]
    generated_pos = positions[generated_mask]
    
    savemat(mat_file, {
        'all_wap_data': wap_data,
        'all_positions': positions,
        'original_wap_data': original_wap,
        'generated_wap_data': generated_wap,
        'original_positions': original_pos,
        'generated_positions': generated_pos,
        'wap_columns': wap_columns,
        'data_source': data_source,
        'building_id': building_id,
        'floor_id': floor_id,
        'dataset_info': f'Augmented dataset for Building {building_id} Floor {floor_id}'
    })
    print(f"增强数据集已保存到: {mat_file}")

def main():
    """主函数"""
    print("=== WiFi指纹数据扩充最终生成 ===")
    
    BUILDING_ID = 0
    FLOOR_ID = 3
    NUM_GENERATED_SAMPLES = 1000
    
    # 1. 加载预处理数据
    print("\n1. 加载预处理数据...")
    preprocessor = DataPreprocessor()
    preprocessor.load_data()
    
    wap_features, positions, wap_columns, original_data = preprocessor.extract_target_data(
        building_id=BUILDING_ID, floor_id=FLOOR_ID
    )
    
    normalized_data, processed_data = preprocessor.preprocess_wap_data(wap_features)
    
    # 2. 加载训练好的模型
    print("\n2. 加载训练好的模型...")
    wgan_gp = WGAN_GP(
        data_dim=normalized_data.shape[1],
        noise_dim=100,
        lr=2e-4,
        lambda_gp=10,
        n_critic=3
    )
    
    model_path = 'models/wgan_gp_epoch_100.pth'
    if os.path.exists(model_path):
        wgan_gp.load_models(model_path)
        print(f"模型已从 {model_path} 加载")
    else:
        print("未找到训练好的模型")
        return
    
    # 3. 生成数据
    print(f"\n3. 生成{NUM_GENERATED_SAMPLES}个样本...")
    generated_normalized = wgan_gp.generate_samples(NUM_GENERATED_SAMPLES)
    generated_processed = process_generated_data(generated_normalized)
    
    print(f"生成数据形状: {generated_processed.shape}")
    print(f"生成数据范围: [{generated_processed.min():.2f}, {generated_processed.max():.2f}]")
    
    # 4. 计算评价指标
    print("\n4. 计算评价指标...")
    metrics = compute_simple_metrics(processed_data, generated_processed)
    
    print(f"KL散度: {metrics['kl_divergence']:.6f}")
    print(f"JS散度: {metrics['js_divergence']:.6f}")
    print(f"Wasserstein距离: {metrics['wasserstein_distance']:.6f}")
    
    # 5. 合并数据集
    print("\n5. 合并数据集...")
    merged_df = merge_datasets(original_data, generated_processed, wap_columns, positions)
    
    # 6. 保存最终数据集
    print("\n6. 保存最终数据集...")
    save_final_dataset(merged_df, BUILDING_ID, FLOOR_ID)
    
    # 7. 生成报告
    print("\n=== 最终报告 ===")
    print(f"原始数据集大小: {len(original_data)}")
    print(f"生成数据集大小: {NUM_GENERATED_SAMPLES}")
    print(f"合并后数据集大小: {len(merged_df)}")
    print(f"数据扩充比例: {NUM_GENERATED_SAMPLES/len(original_data)*100:.1f}%")
    
    print(f"\nWAP特征数量: {len(wap_columns)}")
    print(f"位置信息维度: 2 (经度, 纬度)")
    
    original_wap = original_data[wap_columns].values
    print(f"\n原始数据WAP值范围: [{original_wap.min():.2f}, {original_wap.max():.2f}]")
    print(f"生成数据WAP值范围: [{generated_processed.min():.2f}, {generated_processed.max():.2f}]")
    
    print(f"\n=== 生成质量评价指标 ===")
    print(f"KL散度 (越小越好): {metrics['kl_divergence']:.6f}")
    print(f"JS散度 (越小越好): {metrics['js_divergence']:.6f}")
    print(f"Wasserstein距离 (越小越好): {metrics['wasserstein_distance']:.6f}")
    
    # 质量评估
    if metrics['kl_divergence'] < 0.1:
        kl_quality = "优秀"
    elif metrics['kl_divergence'] < 0.5:
        kl_quality = "良好"
    else:
        kl_quality = "一般"
    
    if metrics['js_divergence'] < 0.1:
        js_quality = "优秀"
    elif metrics['js_divergence'] < 0.3:
        js_quality = "良好"
    else:
        js_quality = "一般"
    
    print(f"\n=== 质量评估 ===")
    print(f"KL散度质量: {kl_quality}")
    print(f"JS散度质量: {js_quality}")
    
    print("\n=== 数据扩充完成! ===")
    print("生成的文件:")
    print(f"- 原始数据: processed_data/building{BUILDING_ID}_floor{FLOOR_ID}_original.csv/.mat")
    print(f"- 增强数据: final_dataset/building{BUILDING_ID}_floor{FLOOR_ID}_augmented.csv/.mat")
    print(f"- 训练模型: models/wgan_gp_epoch_100.pth")
    
    return {
        'merged_dataset': merged_df,
        'original_data': original_data,
        'generated_data': generated_processed,
        'metrics': metrics
    }

if __name__ == "__main__":
    result = main()
