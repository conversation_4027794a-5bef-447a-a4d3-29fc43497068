"""
修复数据中的100值问题
"""

import pandas as pd
import numpy as np
from scipy.io import savemat

def fix_wap_data(df):
    """修复WAP数据中的100值和正数"""
    print("修复WAP数据...")
    
    # 获取WAP列
    wap_columns = [col for col in df.columns if col.startswith('WAP')]
    
    # 检查原始数据
    wap_data = df[wap_columns].values
    print(f"修复前数据范围: [{wap_data.min():.2f}, {wap_data.max():.2f}]")
    print(f"包含100值的数量: {(wap_data == 100).sum()}")
    print(f"包含正数的数量: {(wap_data > 0).sum()}")
    
    # 修复数据
    # 将100替换为-100
    df[wap_columns] = df[wap_columns].replace(100, -100)
    
    # 将所有正数替换为-100
    for col in wap_columns:
        df.loc[df[col] > 0, col] = -100
    
    # 检查修复后的数据
    wap_data_fixed = df[wap_columns].values
    print(f"修复后数据范围: [{wap_data_fixed.min():.2f}, {wap_data_fixed.max():.2f}]")
    print(f"修复后包含100值的数量: {(wap_data_fixed == 100).sum()}")
    print(f"修复后包含正数的数量: {(wap_data_fixed > 0).sum()}")
    
    return df

def main():
    """主函数"""
    print("=== 修复数据中的100值问题 ===")
    
    # 读取数据
    csv_file = 'final_dataset/building0_floor3_augmented.csv'
    print(f"读取文件: {csv_file}")
    df = pd.read_csv(csv_file)
    
    print(f"原始数据形状: {df.shape}")
    
    # 修复WAP数据
    df_fixed = fix_wap_data(df)
    
    # 保存修复后的数据
    print("\n保存修复后的数据...")
    
    # 保存CSV
    fixed_csv = 'final_dataset/building0_floor3_augmented_fixed.csv'
    df_fixed.to_csv(fixed_csv, index=False)
    print(f"修复后的CSV已保存到: {fixed_csv}")
    
    # 保存MAT格式
    fixed_mat = 'final_dataset/building0_floor3_augmented_fixed.mat'
    
    wap_columns = [col for col in df_fixed.columns if col.startswith('WAP')]
    wap_data = df_fixed[wap_columns].values
    positions = df_fixed[['LONGITUDE', 'LATITUDE']].values
    data_source = df_fixed['DATA_SOURCE'].values
    
    original_mask = data_source == 'original'
    generated_mask = data_source == 'generated'
    
    original_wap = wap_data[original_mask]
    generated_wap = wap_data[generated_mask]
    original_pos = positions[original_mask]
    generated_pos = positions[generated_mask]
    
    savemat(fixed_mat, {
        'all_wap_data': wap_data,
        'all_positions': positions,
        'original_wap_data': original_wap,
        'generated_wap_data': generated_wap,
        'original_positions': original_pos,
        'generated_positions': generated_pos,
        'wap_columns': wap_columns,
        'data_source': data_source,
        'building_id': 0,
        'floor_id': 3,
        'dataset_info': 'Fixed augmented dataset for Building 0 Floor 3 (no 100 values or positive values)'
    })
    print(f"修复后的MAT已保存到: {fixed_mat}")
    
    # 验证修复结果
    print("\n=== 验证修复结果 ===")
    wap_data_final = df_fixed[wap_columns].values
    
    print(f"最终数据形状: {wap_data_final.shape}")
    print(f"最终数据范围: [{wap_data_final.min():.2f}, {wap_data_final.max():.2f}]")
    print(f"唯一值数量: {len(np.unique(wap_data_final))}")
    print(f"是否包含100值: {(wap_data_final == 100).any()}")
    print(f"是否包含正数: {(wap_data_final > 0).any()}")
    
    # 显示数据分布
    print(f"\n数据来源分布:")
    print(df_fixed['DATA_SOURCE'].value_counts())
    
    print(f"\n总数据量: {len(df_fixed)}")
    print(f"WAP特征数量: {len(wap_columns)}")
    
    print("\n=== 数据修复完成! ===")
    print("修复后的文件:")
    print(f"- CSV: {fixed_csv}")
    print(f"- MAT: {fixed_mat}")

if __name__ == "__main__":
    main()
